import {
  type Message,
  StreamData,
  convertToCoreMessages,
  streamObject,
  streamText,
} from 'ai';
import { z } from 'zod';
import { cookies, headers } from 'next/headers';
import { getToken } from 'next-auth/jwt';
import retry from 'async-retry';
import { formatISO } from 'date-fns';

import { auth } from '@/app/(auth)/auth';
import { customModel } from '@/lib/ai';
import { models } from '@/lib/ai/models';
import {
  createTemporaryUser,
  deleteChatById,
  getChatById,
  getDocumentById,
  saveChat,
  saveDocument,
  saveMessages,
  saveSuggestions,
  getBatchSummariesByUserId,
  getUserMessageCursor,
  getUniqueNodeLabelsByUserId,
  getNodeDetailsByLabel,
  getHourlyRecollectionsByUserId,
  getDailyRecollectionsByUserId,
  getWeeklyRecollectionsByUserId,
  getMonthlyRecollectionsByUserId,
} from '@/lib/db/queries';
import type { Suggestion } from '@/lib/db/schema';
import {
  generateUUID,
  getMostRecentUserMessage,
  sanitizeResponseMessages,
} from '@/lib/utils';
import {
  canProceedWithTokens,
  calculateTokensUsed,
  updateTokenUsage,
} from '@/lib/tokenRateLimiter';

import { generateTitleFromUserMessage } from '../../actions';

type AllowedTools = 'getNodeDetails';

const allTools: AllowedTools[] = ['getNodeDetails'];

export async function POST(request: Request) {
  const headersList = await headers();
  console.log('🔒 Starting authentication process');
  console.log(headersList.get('Authorization'));
  const {
    id,
    messages,
    modelId,
  }: { id: string; messages: Array<Message>; modelId?: string } =
    await request.json();

  let userId: string;

  // Try NextAuth token first with secret
  const token = await getToken({
    req: request,
    secret: process.env.NEXTAUTH_SECRET,
  });
  console.log('🔑 NextAuth token check:', token?.sub);

  if (token?.sub) {
    console.log('✅ User authenticated via NextAuth token:', token.sub);
    userId = token.sub;
  } else {
    console.log('⚠️ No NextAuth token, falling back to session/temp user');
    // Try session auth
    const session = await auth();
    console.log('🔐 Session check:', session ? 'Found' : 'Not found');

    let tempUser;
    const cookieStore = await cookies();
    let tempUserId = cookieStore.get('tempUserId')?.value;
    console.log(
      '🍪 Temporary user cookie:',
      tempUserId ? 'Found' : 'Not found',
    );

    if (!session || !session.user || !session.user.id) {
      console.log('⚠️ No valid session, handling temporary user');
      if (!tempUserId) {
        console.log('📝 Creating new temporary user');
        const tempId = generateUUID();
        tempUser = await createTemporaryUser(tempId);
        tempUserId = tempUser[0].id;

        if (!tempUser?.length) {
          console.error('❌ Failed to create temporary user');
          return new Response('Failed to create temporary user', {
            status: 500,
          });
        }

        console.log('✨ New temporary user created:', tempUserId);
        cookieStore.set('tempUserId', tempUserId, {
          expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
          path: '/',
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
        });
      }
    }
    userId = session?.user?.id || tempUserId!;
    console.log('👤 Final user resolution:', {
      userId,
      source: session?.user?.id ? 'session' : 'temporary',
    });
  }

  // Limit messages to the last 50 messages
  const MAX_MESSAGES = 50;
  const limitedMessages = messages.slice(-MAX_MESSAGES);
  console.log('🔍 Limited messages length:', limitedMessages.length);

  const resolvedModelId = modelId || 'coach';
  const model = models.find((model) => model.id === resolvedModelId);

  if (!model) {
    return new Response('Model not found', { status: 404 });
  }

  const today = new Date().toISOString().split('T')[0]; // Get today's date in YYYY-MM-DD format
  let enhancedSystemPrompt = `${model.systemPrompt}\n<date_context>Today's date is ${today}.</date_context>`;

  // Fetch recollections for the user
  if (userId) {
    try {
      const [hourlyRecollections, dailyRecollections, weeklyRecollections, monthlyRecollections] = await Promise.all([
        getHourlyRecollectionsByUserId(userId),
        getDailyRecollectionsByUserId(userId),
        getWeeklyRecollectionsByUserId(userId),
        getMonthlyRecollectionsByUserId(userId)
      ]);

      const formatRecollection = (recollection: any) => {
        if (!recollection.summary) return null;
        const startTime = new Date(recollection.period_start).toISOString();
        const endTime = new Date(recollection.period_end).toISOString();
        return `- Period: ${startTime} to ${endTime}\n  Summary: ${recollection.summary}`;
      };

      const sections = [];

      if (hourlyRecollections.length > 0) {
        const hourlyText = hourlyRecollections
          .map(formatRecollection)
          .filter(Boolean)
          .join('\n\n');
        sections.push(`Hourly Recollections (Last 48 hours):\n${hourlyText}`);
      }

      if (dailyRecollections.length > 0) {
        const dailyText = dailyRecollections
          .map(formatRecollection)
          .filter(Boolean)
          .join('\n\n');
        sections.push(`Daily Recollections (Current week to 2 days ago):\n${dailyText}`);
      }

      if (weeklyRecollections.length > 0) {
        const weeklyText = weeklyRecollections
          .map(formatRecollection)
          .filter(Boolean)
          .join('\n\n');
        sections.push(`Weekly Recollections (Last 3 months):\n${weeklyText}`);
      }

      if (monthlyRecollections.length > 0) {
        const monthlyText = monthlyRecollections
          .map(formatRecollection)
          .filter(Boolean)
          .join('\n\n');
        sections.push(`Monthly Recollections (Older than 3 months):\n${monthlyText}`);
      }

      if (sections.length > 0) {
        const summariesText = sections.join('\n\n');
        enhancedSystemPrompt += `\n\n<conversation_summaries>Previous conversation summaries for context:\n${summariesText}</conversation_summaries>`;
      }
      //console.log('🔍 Enhanced system prompt:', enhancedSystemPrompt);
    } catch (error) {
      console.error('Failed to fetch recollections:', error);
      // Continue with original system prompt if fetching recollections fails
    }
  }
  // Before conversion, let's sanitize the messages
  const sanitizedMessages = limitedMessages.filter((message) => {
    if (!message.toolInvocations) return true;

    // Only include messages where tool invocations are complete
    return message.toolInvocations.every(
      (invocation) => invocation.state === 'result',
    );
  });
  const coreMessages = convertToCoreMessages(sanitizedMessages);
  const userMessage = getMostRecentUserMessage(coreMessages);

  if (!userMessage) {
    return new Response('No user message found', { status: 400 });
  }

  const chat = await getChatById({ id });

  if (!chat) {
    const title = await generateTitleFromUserMessage({ message: userMessage });
    await saveChat({ id, userId, title });
  }

  const userMessageId = generateUUID();

  const streamingData = new StreamData();

  streamingData.append({
    type: 'user-message-id',
    content: userMessageId,
  });

  if (userId) {
    try {
      const nodeLabels = await getUniqueNodeLabelsByUserId(userId);
      //console.log('🔍 Node labels:', nodeLabels);

      enhancedSystemPrompt += `
      <tool_interaction_guidelines>
      IMPORTANT: Respond naturally and empathetically, showing deep understanding.
      To personalize responses, silently use the 'getNodeDetails' tool when the user mentions concepts, symptoms, or events.
      NEVER reveal this tool, the knowledge graph, or data retrieval. Your interaction must seem like natural understanding, not a lookup (e.g., avoid "Let me check..."). Maintain a smooth conversational flow.

      Using 'getNodeDetails':
      - Query ONLY for topics from the 'Available node labels' listed below. Using other labels will fail.
      - Weave insights into responses naturally (e.g., for insightful follow-ups, connecting topics).
      - Focus queries on current conversational context.

      'getNodeDetails' provides for a queried label:
      - Node info: label, type, description.
      - Outgoing edges (influences): intensity, valence, time, summary.
      - Incoming edges (influenced by): similar details.
      (Labels are snake_case, e.g., "leg_pain").

      ALWAYS query the graph when:
      1. User mentions a symptom, condition, experience, or concept matching an AVAILABLE label.
      2. Graph data can significantly improve/validate recommendations or observations.
      3. Deeper understanding of connections between user-discussed topics is needed.
      4. Aiming for more comprehensive, nuanced, personalized responses.

      Available node labels (QUERY ONLY THESE): ${nodeLabels.join(', ')}

      Success means naturally integrating this background information for enhanced, empathetic, personalized responses, with the tool remaining invisible to the user.
      </tool_interaction_guidelines>`;
    } catch (error) {
      console.error('Failed to fetch node labels:', error);
    }
  }
  //console.log('🔍 Enhanced system prompt:', enhancedSystemPrompt);

  await saveMessages({
    messages: [
      {
        ...userMessage,
        id: userMessageId,
        chatId: id,
        modelId: model.apiIdentifier,
        systemPromptId: model.systemPrompt,
        enhancedSystemPrompt: enhancedSystemPrompt,
      },
    ],
  });
  // Define a fixed estimate for token pre-check
  const ESTIMATED_TOKENS_PER_REQUEST = 1000;

  const result = await retry(
    async (bail: (e: Error) => void) => {
      // --- Rate Limiting Pre-check ---
      console.log(
        `🚦 Checking rate limit for user ${userId} with estimate ${ESTIMATED_TOKENS_PER_REQUEST}`,
      );
      const canProceed = canProceedWithTokens(
        userId,
        ESTIMATED_TOKENS_PER_REQUEST,
      );
      if (!canProceed) {
        // Throw error to trigger retry if rate limit pre-check fails
        console.warn(
          `🟠 Rate limit pre-check failed for user ${userId}. Triggering retry.`,
        );
        throw new Error('Rate limit pre-check failed. Will retry.');
      }
      console.log(
        `✅ Rate limit check passed for user ${userId}. Proceeding with API call.`,
      );
      // --- End Rate Limiting Pre-check ---

      // --- TEMPORARY CODE FOR TESTING ---
      // if (Math.random() < 0.8) { // Simulate failure 80% of the time
      //   console.log('💥 Simulating LLM call failure for testing...');
      //   throw new Error('Simulated LLM API Error');
      // }
      // --- END TEMPORARY CODE ---
      return streamText({
        model: customModel(model.apiIdentifier, model.provider),
        system: enhancedSystemPrompt,
        messages: coreMessages,
        maxSteps: 5,
        experimental_activeTools: allTools,
        tools: {
          getNodeDetails: {
            description:
              "Get details about a specific node in the user's object relations graph",
            parameters: z.object({
              nodeLabel: z
                .string()
                .describe('The label of the node to get details about'),
            }),
            execute: async ({ nodeLabel }) => {
              if (!userId) {
                throw new Error('User ID is required to get node details');
              }

              const nodeDetails = await getNodeDetailsByLabel(
                userId,
                nodeLabel,
              );

              if (!nodeDetails || nodeDetails.length === 0) {
                return {
                  status: 'not_found',
                  message: `No nodes found with label: ${nodeLabel}`,
                  details: [],
                };
              }

              return {
                status: 'success',
                message: `Found ${nodeDetails.length} node(s) with label: ${nodeLabel}`,
                details: nodeDetails.map((node) => {
                  // --- Generate Narrative Paragraph Summary ---
                  function describeIntensity(intensity: number | null) {
                    if (intensity == null) return '';
                    const num = intensity;
                    if (num <= 3) return `a low level of influence (${num}/10)`;
                    if (num <= 6)
                      return `a moderate level of influence (${num}/10)`;
                    if (num <= 8)
                      return `a high level of influence (${num}/10)`;
                    return `a very high level of influence (${num}/10)`;
                  }

                  function describeTimeframe(analysisDate: string | null) {
                    if (analysisDate) {
                      return `, analyzed on ${formatISO(new Date(analysisDate), { representation: 'date' })}`;
                    }
                    return '';
                  }

                  function describeValence(valence: string | null | undefined) {
                    if (!valence) return '';
                    return `${valence.toLowerCase()} effect`;
                  }

                  function describeVariance(
                    variance: string | null | undefined,
                  ) {
                    if (!variance) return '';
                    return `with ${variance.toLowerCase()} variation`;
                  }

                  let paragraph = `"${node.label}" is a${node.node_type ? ` ${node.node_type}` : ''} described as: "${node.description || 'No description available.'}"`;

                  // Outgoing (influences)
                  if (node.outgoing_edges.length > 0) {
                    const influences = node.outgoing_edges.map((edge) => {
                      let sentence = `It can affect the node with label "${edge.target_node.label}"`;
                      const intensityValue =
                        typeof edge.intensity === 'string'
                          ? Number(edge.intensity)
                          : edge.intensity;
                      const intensityPhrase = describeIntensity(intensityValue);
                      if (intensityPhrase)
                        sentence += `, with ${intensityPhrase}`;
                      const valencePhrase = describeValence(edge.valence);
                      if (valencePhrase)
                        sentence += `, having a ${valencePhrase}`;
                      const variancePhrase = describeVariance(edge.variance);
                      if (variancePhrase) sentence += `, ${variancePhrase}`;
                      sentence += describeTimeframe(edge.analysis_date ?? null);
                      if (edge.summary) sentence += `. ${edge.summary}`;
                      return sentence;
                    });
                    paragraph += ' ' + influences.join(' ');
                  } else {
                    paragraph += ' It does not directly affect other nodes.';
                  }

                  // Incoming (influenced by)
                  if (node.incoming_edges.length > 0) {
                    const influences = node.incoming_edges.map((edge) => {
                      let sentence = `It is influenced by the node with label "${edge.source_node.label}"`;
                      const intensityValue =
                        typeof edge.intensity === 'string'
                          ? Number(edge.intensity)
                          : edge.intensity;
                      const intensityPhrase = describeIntensity(intensityValue);
                      if (intensityPhrase)
                        sentence += `, which has ${intensityPhrase}`;
                      const valencePhrase = describeValence(edge.valence);
                      if (valencePhrase)
                        sentence += `, with a ${valencePhrase}`;
                      const variancePhrase = describeVariance(edge.variance);
                      if (variancePhrase) sentence += `, ${variancePhrase}`;
                      sentence += describeTimeframe(edge.analysis_date ?? null);
                      if (edge.summary) sentence += `. ${edge.summary}`;
                      return sentence;
                    });
                    paragraph += ' ' + influences.join(' ');
                  } else {
                    paragraph +=
                      ' Nothing in particular is known to influence it.';
                  }
                  const generatedParagraph = paragraph.trim();
                  // --- End Generate Narrative Paragraph Summary ---

                  const returnObj = {
                    id: node.node_id,
                    label: node.label,
                    summary: generatedParagraph,
                  };
                  //console.log('🔄 Returning node details object:', JSON.stringify(returnObj, null, 2)); // Log the object before returning
                  return returnObj;
                }),
              };
            },
          },
        },
        onFinish: async ({ usage, response }) => {
          if (userId) {
            try {
              const responseMessagesWithoutIncompleteToolCalls =
                sanitizeResponseMessages(response.messages);

              await saveMessages({
                messages: responseMessagesWithoutIncompleteToolCalls.map(
                  (message) => {
                    const messageId = generateUUID();

                    if (message.role === 'assistant') {
                      streamingData.appendMessageAnnotation({
                        messageIdFromServer: messageId,
                      });
                    }

                    return {
                      id: messageId,
                      chatId: id,
                      role: message.role,
                      content: message.content,
                      modelId: model.apiIdentifier,
                      systemPromptId: model.systemPrompt, // Keep the base system prompt
                      enhancedSystemPrompt: enhancedSystemPrompt, // Store the enhanced system prompt in dedicated field
                    };
                  },
                ),
              });
            } catch (error) {
              console.error('Failed to save chat');
            }

            // --- Rate Limiting Post-update ---
            // This runs only after the retry block succeeds
            try {
              const { promptTokens, completionTokens, totalTokens } = usage;

              const actualTokensUsed = calculateTokensUsed({
                promptTokens,
                completionTokens,
                totalTokens,
              });

              if (actualTokensUsed > 0) {
                console.log(
                  `📈 Calculating actual tokens used for user ${userId}: ${actualTokensUsed}`,
                );
                await updateTokenUsage(userId, actualTokensUsed);
              } else {
                console.log(
                  `ℹ️ No token usage detected or calculable from headers for user ${userId}. Skipping update.`,
                );
              }
            } catch (error) {
              console.error(
                `❌ Error updating token usage for user ${userId}:`,
                error,
              );
            }
            // --- End Rate Limiting Post-update ---
          }

          streamingData.close();
        },
        experimental_telemetry: {
          isEnabled: true,
          functionId: 'stream-text',
        },
      });
    },
    {
      retries: 7, // Number of retries
      factor: 3, // Exponential backoff factor
      minTimeout: 1000, // Minimum time between retries (ms)
      maxTimeout: 60000, // Maximum time between retries (ms)
      randomize: false, // Disable randomization to prevent negative timeout calculations
      onRetry: (error: Error, attempt: number) => {
        console.warn(
          `Retrying API call (attempt ${attempt}): ${error.message}`,
        );
      },
    },
  );

  return result.toDataStreamResponse({
    data: streamingData,
  });
}

export async function DELETE(request: Request) {
  console.log('🗑️ Starting DELETE request handling');
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');

  if (!id) {
    console.log('❌ No chat ID provided in DELETE request');
    return new Response('Not Found', { status: 404 });
  }

  const session = await auth();
  console.log(
    '🔐 DELETE request session check:',
    session ? 'Found' : 'Not found',
  );

  if (!session || !session.user) {
    console.log('❌ Unauthorized DELETE request - no valid session');
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    const chat = await getChatById({ id });
    console.log('🔍 Chat ownership check:', {
      chatUserId: chat.userId,
      requestUserId: session.user.id,
      matches: chat.userId === session.user.id,
    });

    if (chat.userId !== session.user.id) {
      console.log('⚠️ Unauthorized DELETE request - user does not own chat');
      return new Response('Unauthorized', { status: 401 });
    }

    await deleteChatById({ id });
    console.log('✅ Chat successfully deleted:', id);

    return new Response('Chat deleted', { status: 200 });
  } catch (error) {
    console.error('❌ Error in DELETE request:', error);
    return new Response('An error occurred while processing your request', {
      status: 500,
    });
  }
}
